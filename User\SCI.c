/*
 * SCI.c
 *
 *  Created on: 2024年7月22日
 *      Author: Adam
 */

/* ********** LIBRARY CODE BEGIN ********** */

#include "SCI.h"
#include "string.h"
#include "stdio.h"
#include "stdarg.h"
#include "stdint.h"

void SCI_SR_RxByte(SCI_HandleTypeDef *hSCI, uint8_t Byte)
{
    hSCI->Rx_Buf[hSCI->Rx_Count] = Byte;

    if (hSCI->Rx_Count < sizeof(hSCI->Rx_Buf) - 1) // 溢出检查, 正常接收时, 记录接收数量, 空闲重新倒计时, 缓冲区满时, 空闲倒计时直接归零
    {
        hSCI->Rx_Count++;
        hSCI->Rx_IdleCount = hSCI->Rx_IdleTimout;
    }
    else
    {
        hSCI->Rx_IdleCount = 0;
    }
}

void SCI_SR_TIM1ms(SCI_HandleTypeDef *hSCI)
{
    if (hSCI->Rx_IdleCount)
    {
        hSCI->Rx_IdleCount--;
    }
}

void SCI_SR_Loop(SCI_HandleTypeDef *hSCI)
{
    if (hSCI->Rx_Count != 0 && hSCI->Rx_IdleCount == 0)
    {
        hSCI->Rx_Buf[hSCI->Rx_Count] = 0;
        hSCI->Rx_IdleCallbackFunc(hSCI->Rx_Buf, hSCI->Rx_Count);

        hSCI->Rx_Count = 0;
    }

    if (hSCI->Tx_Count != 0)
    {
        for (hSCI->Tx_Index = 0; hSCI->Tx_Index < hSCI->Tx_Count; hSCI->Tx_Index++)
        {
            hSCI->Tx_ByteCallbackFunc(hSCI->Tx_Buf[hSCI->Tx_Index]);
        }
        hSCI->Tx_Count = 0;
    }
}

int SCI_Transmit(SCI_HandleTypeDef *hSCI, uint8_t *pData, uint16_t Size)
{
    if (hSCI->Tx_Count + Size > sizeof(hSCI->Tx_Buf)) // 溢出检查
    {
        Size = sizeof(hSCI->Tx_Buf) - hSCI->Tx_Count;
    }

    memcpy(hSCI->Tx_Buf + hSCI->Tx_Count, pData, Size);
    hSCI->Tx_Count += Size;

    return Size;
}

int SCI_Printf(SCI_HandleTypeDef *hSCI, const char *format, ...)
{
    char Str[256];

    va_list arg;
    va_start(arg, format);
    vsnprintf(Str, sizeof(Str) - 1, format, arg);
    va_end(arg);

    return SCI_Transmit(hSCI, (uint8_t*) Str, strlen(Str) );
}

void SCI_Init(SCI_HandleTypeDef *hSCI, void (*Tx_ByteCallbackFunc)(uint8_t Byte), void (*Rx_IdleCallbackFunc)(uint8_t *Rx_Buf, uint16_t Rx_Count), uint16_t Rx_IdleTimout)
{
    memset(hSCI, 0, sizeof(SCI_HandleTypeDef));

    hSCI->Tx_ByteCallbackFunc = Tx_ByteCallbackFunc;
    hSCI->Rx_IdleCallbackFunc = Rx_IdleCallbackFunc;
    hSCI->Rx_IdleTimout = Rx_IdleTimout;
}

/* ********** LIBRARY CODE END ********** */
// User code
#include "solar.h"

SCI_HandleTypeDef hScia;
SCI_HandleTypeDef hScib;

int Solar_SciBlockTransmit8bit(SCI_HandleTypeDef *hSCI, const uint16_t *pDataAdr16, uint16_t Size) // 阻塞
{
    int Tx_Count = Size;
    int Tx_Index = 0;

    for (Tx_Index = 0; Tx_Count; Tx_Index++)
    {
        // TODO 发送字节
        hSCI->Tx_ByteCallbackFunc(pDataAdr16[Tx_Index] & 0xFF);
        Tx_Count--;

        if (Tx_Count)
        {
            hSCI->Tx_ByteCallbackFunc(pDataAdr16[Tx_Index] >> 8);
            Tx_Count--;
        }
    }

    return Tx_Index;
}

void Scia_Rx_CallbackFunc(uint8_t *Rx_Buf, uint16_t Rx_Count)
{
    //    SCI_SendData(&hScia, Rx_Buf, Rx_Count);

    char cmd[32] = { 0 };
    int value = 0;
    char *str = (char*) Rx_Buf;

    if (0 == strncmp(str, "SET", 3))
    {
        sscanf(str, "SET:%d=>%30s", &value, cmd);

        if (0 == strcmp(cmd, "POWER_TARGET"))
        {
            h.hBoost.power_target = value;
        }
        else if (0 == strncmp(cmd, "POWER_SW", 8))
        {
            h.Ctrl_Power_SW = value;
        }
        else if (0 == strncmp(cmd, "THETA_PLUS", 10))
        {
            hdebug.cntl_value += 0.001; //* ((float)M_PI / 180.0f);
        }
        else if (0 == strncmp(cmd, "THETA_MINUS", 11))
        {
            hdebug.cntl_value -= 0.001; //* ((float)M_PI / 180.0f);
        }
    }
}

// 发送字节回调函数
void Scia_Tx_CallbackFunc(uint8_t Byte)
{
    Uint32 timeout = 99999;
    while (SciaRegs.SCICTL2.bit.TXRDY == 0 && timeout)
    {
        timeout--; // 等待发送就绪
    }
    SciaRegs.SCITXBUF = Byte;
    while (SciaRegs.SCICTL2.bit.TXRDY == 0 && timeout)
    {
        timeout--; // 等待发送就绪
    }
}

void Solar_Scia_Init(uint32_t BaudRate)
{
    SCI_Init(&hScia, Scia_Tx_CallbackFunc, Scia_Rx_CallbackFunc, 20);

    //    InitSci();
    InitSciaGpio(); // 对于这个示例,只初始化Scia GPIO模块
    InitScia(BaudRate, MODE_INT);

    SCI_Printf(&hScia, "Scia Init\r\n");
}

/// @brief
/// @param Rx_Buf
/// @param Rx_Count
// void Scib_Rx_CallbackFunc(uint8_t *Rx_Buf, uint16_t Rx_Count)
//{
//     SCI_SendData(&hScib, Rx_Buf, Rx_Count);
// }

// 发送字节回调函数
void Scib_Tx_CallbackFunc(uint8_t Byte)
{
    Uint32 timeout = 99999;
    while (ScibRegs.SCICTL2.bit.TXRDY == 0 && timeout)
    {
        timeout--; // 等待发送就绪
    }
    ScibRegs.SCITXBUF = Byte;
    while (ScibRegs.SCICTL2.bit.TXRDY == 0 && timeout)
    {
        timeout--; // 等待发送就绪
    }
}

void scib_rx_idle_cb(uint8_t *Rx_Buf, uint16_t Rx_Count);
void Solar_Scib_Init(uint32_t BaudRate)
{
    SCI_Init(&hScib, Scib_Tx_CallbackFunc, scib_rx_idle_cb, 20);

    //    InitSci();
    InitScibGpio(); // 对于这个示例,只初始化Scib GPIO模块
    InitScib(BaudRate, MODE_INT);

    SCI_Printf(&hScib, "Scib Init\r\n");
}
